# Redis & BullMQ Improvements

## 🎯 **Overview**

This document outlines the improvements made to the Redis key structure and BullMQ implementation before migrating to MQTT.

## 🔧 **Redis Key Structure Improvements**

### **Before (Messy Structure)**
```typescript
export const REDIS_KEYS = {
  ACTIVE_LISTINGS: 'active-listings',        // Inconsistent naming
  SESSION_HASH: 'active-sessions',           // Multiple keys pattern
  SESSION_EXPIRY_SET: 'session-expiry',      // Mixed data structures
  SESSION_IDS: 'session-ids',               // Confusing mapping
  DEVICE_IDS: 'device-ids',                 // Set-based storage
  SESSION_CLEANUP: 'session-cleanup',        // Deprecated
  SESSION_ENTRY_CHECK: 'session-entry-check', // Long names
  SESSION_ENTRY_EXPIRED: 'session-entry-expired' // Unused
}
```

### **After (Organized Structure)**
```typescript
export const REDIS_KEYS = {
  // Listings - Hash structure for efficient lookups
  LISTINGS: {
    ACTIVE: 'gomama:listings:active',           // Hash: {listing_id: status}
    STATUS_HISTORY: 'gomama:listings:history',  // Hash: {listing_id: json_history}
  },
  
  // Sessions - Organized by data type
  SESSIONS: {
    ACTIVE: 'gomama:sessions:active',           // Hash: {session_id: json_data}
    BY_USER: 'gomama:sessions:by_user',         // Hash: {user_id: session_id}
    EXPIRY: 'gomama:sessions:expiry',           // Sorted Set: {session_id: expiry_timestamp}
    ENTRY_CHECK: 'gomama:sessions:entry_check', // Sorted Set: {session_id: deadline_timestamp}
  },
  
  // Users & Devices
  USERS: {
    DEVICES: 'gomama:users:devices',            // Hash: {user_id: json_device_array}
    SESSIONS: 'gomama:users:sessions',          // Hash: {user_id: current_session_id}
  },
  
  // Pub/Sub channels for real-time updates
  CHANNELS: {
    LISTING_STATUS: 'gomama:channels:listing_status',     // Pattern: listing_status:{listing_id}
    SESSION_UPDATE: 'gomama:channels:session_update',     // Pattern: session_update:{session_id}
    USER_NOTIFICATION: 'gomama:channels:user_notification', // Pattern: user_notification:{user_id}
  },
  
  // System keys
  SYSTEM: {
    METRICS: 'gomama:system:metrics',
    HEALTH: 'gomama:system:health',
  }
}
```

### **Key Improvements**
✅ **Consistent naming**: All keys use `gomama:` prefix and consistent separators  
✅ **Logical grouping**: Related keys are grouped together  
✅ **Clear data structures**: Each key's data type is documented  
✅ **Namespace isolation**: System keys are separated from business logic  
✅ **Helper functions**: `RedisKeyBuilder` for dynamic key construction  

## 🚀 **BullMQ Improvements**

### **Before (Basic Implementation)**
```typescript
// Only one queue
const sessionQueue = new Queue('session-cleanup', {
  connection: redisClient
})

// Manual queue processing mixed with BullMQ
const FEEDBACK_NOTIFICATION_QUEUE = 'feedback_notification_queue'
await redisClientInstance.rpush(FEEDBACK_NOTIFICATION_QUEUE, ...)
```

### **After (Professional Implementation)**
```typescript
// Multiple organized queues
export const queues = {
  sessionCleanup: new Queue('gomama:session-cleanup', queueConfig),
  notifications: new Queue('gomama:notifications', queueConfig),
  systemTasks: new Queue('gomama:system-tasks', queueConfig),
}

// Proper job type definitions
export interface SessionCleanupJob {
  sessionId: string
  userId?: string
  reason?: 'expired' | 'terminated' | 'idle'
}

// Centralized queue management
export const QueueManager = {
  addSessionCleanup: (data: SessionCleanupJob) => 
    queues.sessionCleanup.add('cleanup', data),
  
  addNotification: (data: NotificationJob) => 
    queues.notifications.add(data.type, data),
    
  getQueueStats: async () => { /* monitoring */ }
}
```

### **BullMQ Improvements**
✅ **Multiple specialized queues** instead of one generic queue  
✅ **Proper retry configuration** with exponential backoff  
✅ **Type-safe job definitions** with TypeScript interfaces  
✅ **Dedicated workers** with error handling and logging  
✅ **Queue monitoring** and metrics collection  
✅ **Centralized job management** through QueueManager  

## 📊 **Data Structure Optimizations**

### **Device Storage**
**Before**: Multiple Redis sets (`device-ids:{user_id}`)  
**After**: Single hash with JSON arrays (`gomama:users:devices`)

### **Session Mapping**
**Before**: Confusing bidirectional mapping  
**After**: Clear unidirectional mapping with separate keys

### **Pub/Sub Channels**
**Before**: Inconsistent channel naming  
**After**: Organized channel hierarchy with helper functions

## 🔄 **Migration Strategy**

1. **Migration Script**: `migrate-redis-keys.ts` handles data migration
2. **Backward Compatibility**: Old code updated to use new keys
3. **Gradual Rollout**: Can run both systems temporarily
4. **Rollback Plan**: Migration script can be reversed if needed

## 📈 **Performance Benefits**

- **Reduced Redis calls**: Better data structure choices
- **Improved caching**: Consistent key patterns enable better caching
- **Better monitoring**: Queue stats and system metrics
- **Cleaner code**: Centralized key management and queue operations

## 🛠️ **Files Modified**

- `redis.ts` - New key structure and helper functions
- `queue.ts` - Improved BullMQ implementation
- `workers.ts` - NEW - Dedicated worker processes
- `firebase.ts` - Updated to use new keys and queues
- `events.ts` - Updated Redis key event handling
- `server.ts` - Added worker startup and enhanced metrics
- `migrate-redis-keys.ts` - NEW - Migration script

## 🚀 **Next Steps for MQTT Migration**

With this clean Redis/BullMQ foundation:

1. **MQTT topics** can map cleanly to Redis channels
2. **Queue system** can handle MQTT message processing
3. **Monitoring** is already in place for the transition
4. **Key structure** provides clear data organization

The improved architecture makes the MQTT migration much cleaner and more maintainable.
