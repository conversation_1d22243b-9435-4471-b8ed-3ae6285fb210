################## First Stage - Creating base #########################

# Created a variable to hold our node base image
ARG NODE_IMAGE=node:lts-alpine3.19

# Using the variable to create our base image
FROM $NODE_IMAGE AS base
# Running a command to install dumb-init to handle processes
RUN apk --no-cache add dumb-init
# Running a command to install python
# RUN apk --no-cache add python3 py3-pip
# Running a command to install utils
# RUN apk --no-cache add make gcc g++
# Running a command to install gcompat
RUN apk --no-cache add gcompat
# Running a command to install git
RUN apk --no-cache add git
# Running a command to install curl
RUN apk --no-cache add curl
# Creating folders and changing ownerships
RUN mkdir -p /home/<USER>/app && chown node:node /home/<USER>/app
# Setting the working directory
WORKDIR /home/<USER>/app
# Changing the current active user to "node"
USER node
# Creating a new folder "tmp"
RUN mkdir tmp

################## Second Stage - Installing dependencies ##########

# In this stage, we will start installing dependencies
FROM base AS dependencies
# We copy all package & lock files to the working directory
COPY --chown=node:node ./package.json ./
COPY --chown=node:node ./yarn.lock ./
# We run NPM CI to install the exact versions of dependencies
RUN yarn install --frozen-lockfile
# Lastly, we copy all the files with active user
COPY --chown=node:node . .

################## Third Stage - Building Stage ##############c#######

# In this stage, we will start building dependencies
FROM dependencies AS build
# We run "node ace build" to build the app for production
RUN yarn build

################## Final Stage - Production #########################

# In this final stage, we will start running the application
FROM base AS production
# Here, we include all the required environment variables
ENV NODE_ENV=production
# Copy all package & lock files to the working directory with active user
COPY --chown=node:node ./package.json ./
COPY --chown=node:node ./yarn.lock ./
# Copy serviceAccountKey.json
COPY --chown=node:node ./serviceAccountKey.json .
# We run NPM CI to install the exact versions of dependencies
RUN yarn install --frozen-lockfile --production
# Copy files to the working directory from the build folder the user
COPY --chown=node:node --from=build /home/<USER>/app/dist .
# Expose port
EXPOSE $PORT
# Set Node.js options for better performance and memory management
ENV NODE_OPTIONS="--max-old-space-size=512 --max-http-header-size=16384 --expose-gc"
# Run the command to start the server using "dumb-init"
CMD [ "dumb-init", "node", "server.js" ]

# command to build
# docker build --platform linux/amd64 -t gaincue/gomama-realtime .
# docker push gaincue/gomama-realtime

# for macos
# docker buildx build --platform linux/arm64 -t gaincue/gomama-realtime:arm64 .
