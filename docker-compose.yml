services:
  gomama_realtime:
    container_name: gomama_realtime
    # local dev
    image: gaincue/gomama-realtime:arm64
    # image: gaincue/gomama_realtime:latest
    restart: unless-stopped
    env_file:
      - .env
    ports:
      - 9001:9001
    # Resource limits to prevent excessive CPU usage
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    # Health check to ensure the service is running properly
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # local dev
    network_mode: "host"
    # networks:
    #   - gomama
