import Redis from 'ioredis'
import { ListingSession, TopicWebSocket } from './types'

export const REDIS_KEYS = {
  ACTIVE_LISTINGS: 'active-listings',
  SESSION_HASH: 'active-sessions', // Hash storing all sessions
  SESSION_EXPIRY_SET: 'session-expiry', // Sorted set for session expiry times
  SESSION_IDS: 'session-ids', // Set of active session IDs
  DEVICE_IDS: 'device-ids', // Set of user id mapping to device id
  SESSION_CLEANUP: 'session-cleanup',
  SESSION_ENTRY_CHECK: 'session-entry-check', // Track sessions to join in 5 minutes
  SESSION_ENTRY_EXPIRED: 'session-entry-expired'
} as const

export let redisClient: Redis

export async function setupRedis(redisUrl: string) {
  redisClient = new Redis(redisUrl)
  const redisSubscriber = redisClient.duplicate()

  await redisSubscriber.psubscribe('*')
  console.log('Subscribed to Redis channels')

  return { redisClient, redisSubscriber }
}

export async function handleSubscribe(ws: TopicWebSocket, topic: string, redisClient: Redis) {
  // Add topic to user's subscribed topics
  ws.getUserData().subscribedTopics.add(topic)
  ws.subscribe(topic)
  console.log(`Client subscribed to topic: ${topic}`)

  try {
    // Parse topic format (topic:id)
    const [key, id] = topic.split(':')

    if (!key || !id) {
      console.error(`Invalid topic format: ${topic}`)
      return
    }

    // Handle different types of subscriptions more efficiently
    if (key === 'active-listings') {
      // For active listings, just get the specific listing status
      const initialData = await redisClient.hget(key, id)

      if (initialData) {
        ws.send(JSON.stringify({ topic, data: initialData }))
      }
    } else {
      // For other topics (like session data)
      const redisData = await redisClient.hgetall(topic)

      if (isEmpty(redisData)) {
        return // No data to send
      }

      // Process data based on whether it's a ListingSession or not
      if (redisData['id'] == null) {
        // Simple JSON data
        ws.send(JSON.stringify({ topic, data: JSON.stringify(redisData) }))
      } else {
        // ListingSession data
        try {
          const listingSession = ListingSession.fromJSON(redisData)
          ws.send(JSON.stringify({ topic, data: JSON.stringify(listingSession) }))
        } catch (error) {
          console.error(`Error processing ListingSession data for topic ${topic}:`, error)
        }
      }
    }
  } catch (error) {
    console.error(`Error in handleSubscribe for topic ${topic}:`, error)
  }
}

export function handleUnsubscribe(ws: TopicWebSocket, topic: string) {
  ws.getUserData().subscribedTopics.delete(topic)
  ws.unsubscribe(topic)
  console.log(`Client unsubscribed from topic: ${topic}`)
}

export async function scanAllKeys(pattern: string): Promise<string[]> {
  const keys: string[] = []
  let cursor = '0'

  do {
    const [nextCursor, elements] = await redisClient.scan(cursor, 'MATCH', pattern)
    keys.push(...elements)
    cursor = nextCursor
  } while (cursor !== '0')

  return keys
}

const isEmpty = (value: unknown): value is null | undefined | '' | Record<never, never> => {
  if (value === null || value === undefined || value === '') return true
  if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
    return true
  return false
}
