import * as uWS from 'uWebSockets.js'
import { Redis } from 'ioredis'
import { UserData, TopicWebSocket } from './types'
import { handleSubscribe, handleUnsubscribe } from './redis'
import jwt from 'jsonwebtoken'
import dotenv from 'dotenv'

dotenv.config()

// Store all active connections
const clients: Set<TopicWebSocket> = new Set()

// Function to verify JWT token
function verifyToken(token: string): { userId: string } | null {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { userId: string }
    return decoded
  } catch (err) {
    return null
  }
}

export function setupWebSocket(redisClient: Redis): uWS.WebSocketBehavior<UserData> {
  return {
    /* There are many common uWS.WebSocketBehavior options, see the official uWS.js docs */
    /* We recommend having a timeout for incoming messages, and a backpressure drain */
    compression: uWS.SHARED_COMPRESSOR,
    maxPayloadLength: 16 * 1024 * 1024,
    idleTimeout: 10,
    /* Handlers */
    upgrade: (res, req, context) => {
      const token = req.getQuery('token')
      if (!token) {
        res.writeStatus('401 Unauthorized').end()
        return
      }

      const decoded = verifyToken(token)
      if (!decoded) {
        res.writeStatus('403 Forbidden').end()
        return
      }

      // You can attach the decoded user information to the WebSocket
      // for later use in the 'open' handler or other handlers.
      // For example, by passing it as a custom user data object.
      res.upgrade(
        { userId: decoded.userId }, // This object will be available as ws.getUserData()
        req.getHeader('sec-websocket-key'),
        req.getHeader('sec-websocket-protocol'),
        req.getHeader('sec-websocket-extensions'),
        context
      )
    },
    open: (ws: TopicWebSocket) => {
      console.log('A WebSocket connected!')
      ws.getUserData().subscribedTopics = new Set()
      clients.add(ws)
      // You can now access ws.getUserData().userId here
      // console.log(`User ${ws.getUserData().userId} connected via WebSocket.`)
    },
    message: (ws: TopicWebSocket, message: ArrayBuffer) => {
      const messageString = Buffer.from(message).toString()
      const { action, topic } = JSON.parse(messageString)

      switch (action) {
        case 'subscribe':
          handleSubscribe(ws, topic, redisClient)
          break
        case 'unsubscribe':
          handleUnsubscribe(ws, topic)
          break
        default:
          console.log('Unknown action:', action)
      }
    },
    close: (ws: TopicWebSocket, code: number, message: ArrayBuffer) => {
      console.log('WebSocket disconnected with code:', code)
      /* The library guarantees proper unsubscription at close */
      // Remove client from the set to prevent memory leaks
      clients.delete(ws)

      // Clear any other resources associated with this client
      const userData = ws.getUserData()
      if (userData.subscribedTopics) {
        userData.subscribedTopics.clear()
      }

      if (userData.firebaseUnsubscribe) {
        userData.firebaseUnsubscribe()
      }
    }
  }
}
