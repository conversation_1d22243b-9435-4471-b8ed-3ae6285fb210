import * as uWS from 'uWebSockets.js'
import dotenv from 'dotenv'
import { setupWebSocket } from './websocket'
import { setupRedis } from './redis'
import { setupFirebase } from './firebase'
import { setupRedisKeyEvents } from './events'
import { setupMonitoring, getSystemMetrics } from './monitoring'
import { workers } from './workers'
import { QueueManager } from './queue'

dotenv.config()
const port = Number(process.env.PORT || 9001)
const redisUrl = `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`

async function startServer() {
  // Initialize monitoring
  setupMonitoring()

  const { redisClient, redisSubscriber } = await setupRedis(redisUrl)
  setupFirebase(redisClient)
  setupRedisKeyEvents(redisSubscriber)

  // Start BullMQ workers
  console.log('Starting BullMQ workers...')
  Object.values(workers).forEach(worker => {
    console.log(`Started worker: ${worker.name}`)
  })

  const app = uWS
    .App()
    .ws('/*', setupWebSocket(redisClient))
    // ------------------------
    // gomama_rt broadcast
    // ------------------------

    // Add a metrics endpoint
    .get('/metrics', async (res, req) => {
      const metricsApiKey = process.env.METRICS_API_KEY
      if (!metricsApiKey || req.getHeader('authorization') !== `Bearer ${metricsApiKey}`) {
        res.writeStatus('401 Unauthorized').end('Unauthorized')
        return
      }

      const systemMetrics = getSystemMetrics()
      const queueStats = await QueueManager.getQueueStats()

      const metrics = {
        ...systemMetrics,
        queues: queueStats,
        timestamp: Date.now()
      }

      res.cork(() => {
        res.writeHeader('Content-Type', 'application/json')
        res.writeStatus('200 OK').end(JSON.stringify(metrics, null, 2))
      })
    })
    .any('/*', (res, req) => {
      res.end('Nothing to see here!')
    })
    .listen(port, (token) => {
      if (token) {
        console.log('Listening to port ' + port)
      } else {
        console.log('Failed to listen to port ' + port)
      }
    })

  // Setup Redis subscriber
  redisSubscriber.on('pmessage', (_: string, channel: string, message: string) => {
    console.log("Received from channel '%s'", channel)
    app.publish(channel, JSON.stringify({ topic: channel, data: message }))
  })

  // Setup session checking
}

startServer().catch(console.error)

// ------------------------
// gomama_rt broadcast
// ------------------------
// .post("/broadcast", (res, req) => {
//   readJson(
//     res,
//     (body: { topic: string; data: string }) => {
//       const topic = body.topic
//       const data = body.data

//       if (topic && data) {
//         app.publish(topic, data)
//       }

//       res.writeStatus("200 OK").end("Message published")
//     },
//     () => {
//       /* Request was prematurely aborted or invalid or missing, stop reading */
//       console.log("Invalid JSON or no data at all!")
//       res.writeStatus("400 Bad Request").end("Invalid JSON")
//     }
//   )
// })

// util functions
function readJson(res: uWS.HttpResponse, cb: (json: any) => void, err: () => void): void {
  let buffer: Buffer | undefined

  /* Register data cb */
  res.onData((ab: ArrayBuffer, isLast: boolean) => {
    const chunk = Buffer.from(ab)
    if (isLast) {
      let json: any
      if (buffer) {
        try {
          json = JSON.parse(Buffer.concat([buffer, chunk]).toString())
        } catch (e) {
          /* res.close calls onAborted */
          res.close()
          return
        }
        cb(json)
      } else {
        try {
          json = JSON.parse(chunk.toString())
        } catch (e) {
          /* res.close calls onAborted */
          res.close()
          return
        }
        cb(json)
      }
    } else {
      if (buffer) {
        buffer = Buffer.concat([buffer, chunk])
      } else {
        buffer = chunk
      }
    }
  })

  /* Register error cb */
  res.onAborted(err)
}
