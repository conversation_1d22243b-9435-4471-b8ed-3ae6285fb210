import { Redis } from 'ioredis'
import { REDIS_KEYS } from './redis'
import { QueueManager } from './queue'
import { sendFeedbackNotification } from './firebase'
import { redisClient } from './redis'

export function setupRedisKeyEvents(redisSubscriber: Redis) {
  redisSubscriber.on('message', (channel, message) => {
    // channel will be like '__keyevent@0__:expired'
    // message will be the key that expired, e.g., 'session-expiry:someSessionId'
    console.log(`Redis Key Event: Channel: ${channel}, Message: ${message}`)

    if (channel === `__keyevent@0__:expired`) {
      // Handle expired sessions from SESSION_EXPIRY
      if (message.startsWith(`${REDIS_KEYS.SESSIONS.EXPIRY}:`)) {
        const sessionId = message.split(':')[2] // gomama:sessions:expiry:sessionId
        console.log(`Session ${sessionId} expired. Adding to cleanup queue.`)
        QueueManager.addSessionCleanup({ sessionId, reason: 'expired' })

        // Optionally, send feedback notification here if it's tied to expiry
        // You'll need to fetch userId from Redis using sessionId
        // Example:
        // redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, sessionId).then(userId => {
        //   if (userId) sendFeedbackNotification(sessionId, userId);
        // });
      }

      // Handle idle sessions from SESSION_ENTRY_CHECK
      if (message.startsWith(`${REDIS_KEYS.SESSIONS.ENTRY_CHECK}:`)) {
        const sessionId = message.split(':')[2] // gomama:sessions:entry_check:sessionId
        console.log(`Session ${sessionId} idle entry check expired. Adding to cleanup queue.`)
        // This might need a different job type or logic if it's not a full cleanup
        // For now, assuming it also leads to a cleanup
        QueueManager.addSessionCleanup({ sessionId, reason: 'idle' })

        // Optionally, send feedback notification here if it's tied to idle expiry
        // You'll need to fetch userId from Redis using sessionId
        // Example:
        // redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, sessionId).then(userId => {
        //   if (userId) sendFeedbackNotification(sessionId, userId);
        // });
      }
    }
  })

  // Subscribe to keyspace events for expired keys
  redisSubscriber.subscribe(`__keyevent@0__:expired`)
  console.log('Subscribed to Redis keyspace events for expired keys.')
}
