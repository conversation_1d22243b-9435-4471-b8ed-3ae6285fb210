# `gomama_realtime` - Real-time Services

## Overview

This repository contains the real-time service for the Gomama platform, built with **uWebSockets.js (TypeScript)**. Its primary role is to manage WebSocket connections with clients and facilitate real-time data exchange between the backend services and the `gomama_flutter_v2` mobile app.

## Key Responsibilities

-   **WebSocket Server:** Maintains persistent WebSocket connections with authenticated clients.
-   **Firebase Integration:** Listens for real-time updates from a designated Firestore collection (e.g., sensor data).
-   **Redis Pub/Sub:**
    -   Subscribes to Redis channels to receive messages published by `gomama_adonis`.
    -   Forwards these messages to the appropriate clients over WebSockets.
    -   Publishes updates to Redis based on events from Firestore.
-   **Client Communication:** Pushes live updates to the Flutter app, such as booking status changes, notifications, and location tracking.

## Tech Stack

-   **Core:** uWebSockets.js (TypeScript)
-   **Real-time Database:** Google Firestore
-   **Messaging:** Redis (Pub/Sub)

## Getting Started

1.  **Install Dependencies:**
    ```bash
    npm install
    ```
2.  **Setup Environment Variables:**
    -   Create a `.env` file.
    -   Provide connection details for Redis and Firebase (service account).
3.  **Start the Development Server:**
    ```bash
    npm run dev
    ```

The WebSocket server will be running on the port specified in your `.env` file (e.g., `9001`).