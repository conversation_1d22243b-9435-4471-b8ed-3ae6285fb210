#!/usr/bin/env ts-node

/**
 * Migration script to transition from old Redis key structure to new organized structure
 * Run this script once during deployment to migrate existing data
 */

import Redis from 'ioredis'
import dotenv from 'dotenv'

dotenv.config()

const redisUrl = `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`
const redis = new Redis(redisUrl)

// Old key structure
const OLD_KEYS = {
  ACTIVE_LISTINGS: 'active-listings',
  SESSION_HASH: 'active-sessions',
  SESSION_EXPIRY_SET: 'session-expiry',
  SESSION_IDS: 'session-ids',
  DEVICE_IDS: 'device-ids',
  SESSION_CLEANUP: 'session-cleanup',
  SESSION_ENTRY_CHECK: 'session-entry-check',
  SESSION_ENTRY_EXPIRED: 'session-entry-expired'
}

// New key structure
const NEW_KEYS = {
  LISTINGS: {
    ACTIVE: 'gomama:listings:active',
    STATUS_HISTORY: 'gomama:listings:history',
  },
  SESSIONS: {
    ACTIVE: 'gomama:sessions:active',
    BY_USER: 'gomama:sessions:by_user',
    EXPIRY: 'gomama:sessions:expiry',
    ENTRY_CHECK: 'gomama:sessions:entry_check',
  },
  USERS: {
    DEVICES: 'gomama:users:devices',
    SESSIONS: 'gomama:users:sessions',
  },
  SYSTEM: {
    METRICS: 'gomama:system:metrics',
    HEALTH: 'gomama:system:health',
  }
}

async function migrateRedisKeys() {
  console.log('🚀 Starting Redis key migration...')
  
  try {
    // 1. Migrate active listings
    await migrateActiveListings()
    
    // 2. Migrate session data
    await migrateSessions()
    
    // 3. Migrate device mappings
    await migrateDevices()
    
    // 4. Migrate expiry and entry check data
    await migrateExpiryData()
    
    // 5. Clean up old keys (optional - comment out for safety)
    // await cleanupOldKeys()
    
    console.log('✅ Migration completed successfully!')
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  } finally {
    await redis.quit()
  }
}

async function migrateActiveListings() {
  console.log('📋 Migrating active listings...')
  
  const exists = await redis.exists(OLD_KEYS.ACTIVE_LISTINGS)
  if (!exists) {
    console.log('  No active listings to migrate')
    return
  }
  
  // Get all listing data
  const listingData = await redis.hgetall(OLD_KEYS.ACTIVE_LISTINGS)
  
  if (Object.keys(listingData).length > 0) {
    // Copy to new key structure
    await redis.hmset(NEW_KEYS.LISTINGS.ACTIVE, listingData)
    console.log(`  Migrated ${Object.keys(listingData).length} listings`)
  }
}

async function migrateSessions() {
  console.log('🔄 Migrating session data...')
  
  // Find all session hash keys (active-sessions:*)
  const sessionKeys = await scanKeys(`${OLD_KEYS.SESSION_HASH}:*`)
  
  if (sessionKeys.length === 0) {
    console.log('  No session data to migrate')
    return
  }
  
  const pipeline = redis.pipeline()
  let sessionCount = 0
  
  for (const oldKey of sessionKeys) {
    const userId = oldKey.split(':')[1]
    const sessionData = await redis.hgetall(oldKey)
    
    if (Object.keys(sessionData).length > 0) {
      // If there's an active session, store it properly
      if (sessionData.id && sessionData.id !== 'no_active_session') {
        // Store session data
        pipeline.hset(NEW_KEYS.SESSIONS.ACTIVE, sessionData.id, JSON.stringify(sessionData))
        
        // Store user -> session mapping
        pipeline.hset(NEW_KEYS.SESSIONS.BY_USER, userId, sessionData.id)
        
        sessionCount++
      }
    }
  }
  
  if (sessionCount > 0) {
    await pipeline.exec()
    console.log(`  Migrated ${sessionCount} active sessions`)
  }
  
  // Migrate session ID mappings
  const sessionIdExists = await redis.exists(OLD_KEYS.SESSION_IDS)
  if (sessionIdExists) {
    const sessionIds = await redis.hgetall(OLD_KEYS.SESSION_IDS)
    if (Object.keys(sessionIds).length > 0) {
      // This data structure changes - old was {sessionId: userId}, new is {userId: sessionId}
      const reversedMapping = {}
      for (const [sessionId, userId] of Object.entries(sessionIds)) {
        reversedMapping[userId] = sessionId
      }
      await redis.hmset(NEW_KEYS.SESSIONS.BY_USER, reversedMapping)
      console.log(`  Migrated ${Object.keys(sessionIds).length} session ID mappings`)
    }
  }
}

async function migrateDevices() {
  console.log('📱 Migrating device mappings...')
  
  // Find all device keys (device-ids:*)
  const deviceKeys = await scanKeys(`${OLD_KEYS.DEVICE_IDS}:*`)
  
  if (deviceKeys.length === 0) {
    console.log('  No device data to migrate')
    return
  }
  
  const pipeline = redis.pipeline()
  let deviceCount = 0
  
  for (const oldKey of deviceKeys) {
    const userId = oldKey.split(':')[1]
    const devices = await redis.smembers(oldKey)
    
    if (devices.length > 0) {
      // Convert set to JSON array and store in hash
      pipeline.hset(NEW_KEYS.USERS.DEVICES, userId, JSON.stringify(devices))
      deviceCount++
    }
  }
  
  if (deviceCount > 0) {
    await pipeline.exec()
    console.log(`  Migrated device data for ${deviceCount} users`)
  }
}

async function migrateExpiryData() {
  console.log('⏰ Migrating expiry and entry check data...')
  
  // Migrate session expiry data
  const expiryExists = await redis.exists(OLD_KEYS.SESSION_EXPIRY_SET)
  if (expiryExists) {
    const expiryData = await redis.zrange(OLD_KEYS.SESSION_EXPIRY_SET, 0, -1, 'WITHSCORES')
    if (expiryData.length > 0) {
      // Convert to new format
      const args = []
      for (let i = 0; i < expiryData.length; i += 2) {
        args.push(expiryData[i + 1], expiryData[i]) // score, member
      }
      await redis.zadd(NEW_KEYS.SESSIONS.EXPIRY, ...args)
      console.log(`  Migrated ${expiryData.length / 2} expiry entries`)
    }
  }
  
  // Migrate entry check data
  const entryCheckExists = await redis.exists(OLD_KEYS.SESSION_ENTRY_CHECK)
  if (entryCheckExists) {
    const entryCheckData = await redis.zrange(OLD_KEYS.SESSION_ENTRY_CHECK, 0, -1, 'WITHSCORES')
    if (entryCheckData.length > 0) {
      const args = []
      for (let i = 0; i < entryCheckData.length; i += 2) {
        args.push(entryCheckData[i + 1], entryCheckData[i]) // score, member
      }
      await redis.zadd(NEW_KEYS.SESSIONS.ENTRY_CHECK, ...args)
      console.log(`  Migrated ${entryCheckData.length / 2} entry check entries`)
    }
  }
}

async function cleanupOldKeys() {
  console.log('🧹 Cleaning up old keys...')
  
  const keysToDelete = [
    OLD_KEYS.ACTIVE_LISTINGS,
    OLD_KEYS.SESSION_EXPIRY_SET,
    OLD_KEYS.SESSION_IDS,
    OLD_KEYS.SESSION_CLEANUP,
    OLD_KEYS.SESSION_ENTRY_CHECK,
    OLD_KEYS.SESSION_ENTRY_EXPIRED,
  ]
  
  // Add pattern-based keys
  const sessionKeys = await scanKeys(`${OLD_KEYS.SESSION_HASH}:*`)
  const deviceKeys = await scanKeys(`${OLD_KEYS.DEVICE_IDS}:*`)
  
  keysToDelete.push(...sessionKeys, ...deviceKeys)
  
  if (keysToDelete.length > 0) {
    await redis.del(...keysToDelete)
    console.log(`  Deleted ${keysToDelete.length} old keys`)
  }
}

async function scanKeys(pattern: string): Promise<string[]> {
  const keys: string[] = []
  let cursor = '0'
  
  do {
    const [nextCursor, elements] = await redis.scan(cursor, 'MATCH', pattern)
    keys.push(...elements)
    cursor = nextCursor
  } while (cursor !== '0')
  
  return keys
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateRedisKeys()
}

export { migrateRedisKeys }
