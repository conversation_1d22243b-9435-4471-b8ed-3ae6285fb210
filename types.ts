import * as uWS from 'uWebSockets.js'

export interface UserData {
  subscribedTopics: Set<string>
  firebaseUnsubscribe?: () => void
}

export type TopicWebSocket = uWS.WebSocket<UserData>

export class ListingSession {
  'expected_ended_at': string
  'lock_daily_pin': string
  'created_at': string
  'user_id': string
  'lock_bluetooth_guest_key': string
  'id': string
  'lock_one_time_pin': string
  'lock_hourly_pin': string
  'firestore_id': string
  'started_at': string
  'updated_at': string
  'entry_deadline': string | number
  'type': string
  'lock_custom_pin': string
  'listing_id': string
  'number_of_usage_extensions': number
  'actual_usage_duration': number
  'expected_usage_duration': number

  constructor(data: any) {
    this.expected_ended_at = data.expected_ended_at
    this.lock_daily_pin = data.lock_daily_pin
    this.created_at = data.created_at
    this.user_id = data.user_id
    this.lock_bluetooth_guest_key = data.lock_bluetooth_guest_key
    this.id = data.id
    this.lock_one_time_pin = data.lock_one_time_pin
    this.lock_hourly_pin = data.lock_hourly_pin
    this.firestore_id = data.firestore_id
    this.started_at = data.started_at
    this.updated_at = data.updated_at
    this.entry_deadline = data.entry_deadline
    this.type = data.type
    this.lock_custom_pin = data.lock_custom_pin
    this.listing_id = data.listing_id
    this.number_of_usage_extensions =
      data.number_of_usage_extensions === '' ? 0 : Number(data.number_of_usage_extensions)
    this.actual_usage_duration =
      data.actual_usage_duration === '' ? 0 : Number(data.actual_usage_duration)
    this.expected_usage_duration = Number(data.expected_usage_duration)
  }

  static fromJSON(json: string): ListingSession {
    const data = typeof json === 'string' ? JSON.parse(json) : json
    return new ListingSession(data)
  }
}
